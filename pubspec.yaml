name: thoughtecho
description: 一款帮助你记录和分析思想的应用。
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'
  flutter: '>=3.22.1'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  provider: ^6.1.2
  sqflite: ^2.3.2
  sqflite_common_ffi: ^2.3.2
  sqlite3_flutter_libs: ^0.5.18
  path: ^1.8.3
  path_provider: ^2.1.2
  dio: ^5.4.0
  uuid: ^4.3.3
  shared_preferences: ^2.5.2
  flutter_markdown: ^0.7.1
  intl: 0.19.0
  url_launcher: ^6.2.4
  file_picker: ^8.1.2 # 更安全的文件选择器，支持 withData: false 参数
  file_selector: ^1.0.2 # 保留用于桌面端
  share_plus: ^11.0.0 # 用于分享文件
  flex_color_scheme: ^8.2.0
  geolocator: ^14.0.2
  permission_handler: ^12.0.1
  flutter_secure_storage: ^9.0.0
  geocoding: ^4.0.0 # 使用系统提供的地理编码功能
  geocode: ^1.0.3 # 轻量级地理编码包，可配合系统功能使用
  mmkv: ^1.3.9 # 使用1.3.x版本以支持32位设备
  ffi: ^2.1.0 # 显式添加FFI依赖，确保与最新的Dart兼容
  sqflite_common_ffi_web: ^1.0.0
  flex_color_picker: ^3.4.1
  lottie: ^3.3.1
  flutter_svg: ^2.0.7
  flutter_spinkit: ^5.2.0
  dynamic_color: ^1.6.6
  flutter_quill: ^11.4.0
  flutter_quill_extensions: ^11.0.0
  video_player: ^2.8.1
  chewie: ^1.7.4
  image_picker: ^1.0.4
  package_info_plus: ^8.3.0 # 更新版本解决依赖冲突
  logging: ^1.2.0 # 添加 logging 包用于日志管理
  logging_flutter: ^3.0.0 # 添加 logging_flutter 包用于Flutter日志管理
  # 性能优化相关包
  infinite_scroll_pagination: ^4.0.0 # 高性能无限滚动分页
  visibility_detector: ^0.4.0 # 可见性检测，实现懒加载  
  audioplayers: ^6.5.0
  archive: ^4.0.7
  flutter_chat_ui: ^1.6.15
  flutter_chat_types: ^3.6.2
  image: ^4.1.3 # 图片处理库
  gal: ^2.3.0 # 替换image_gallery_saver，保存图片到相册，支持最新Android
  vector_graphics: ^1.1.19

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1
  # Testing framework dependencies
  integration_test:
    sdk: flutter
  test: ^1.25.2
  fake_async: ^1.3.1

# 应用图标配置
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "icon.png"
  
# 字体配置
flutter:
  # 确保应用可以使用Material Design图标
  uses-material-design: true
  assets:
    - assets/lottie/
    - assets/icon.png